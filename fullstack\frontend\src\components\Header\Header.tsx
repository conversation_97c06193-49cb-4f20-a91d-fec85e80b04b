import React, { useState } from "react";
import {
  HiBars3,
  HiMagnifyingGlass,
  HiAdjustmentsHorizontal,
  HiListBullet,
  HiArrowLeft,
  HiTrash,
} from "react-icons/hi2";
import { BsPin } from "react-icons/bs";
import { usePicks, SelectedPick } from "../../contexts/PicksContext";
import PickDetailModal from "../PicksView/PickDetailModal";

interface HeaderProps {
  onBackToHome: () => void;
  onSearchChange: (query: string) => void;
  searchValue: string;
}

// Pick interface for modal compatibility
interface Pick {
  id: number;
  playerName: string;
  playerNumber: string;
  betType: string;
  gameInfo: string;
  confidence: number;
  expertCount: number;
  additionalExperts: number;
  handicapperNames: string[];
}

// Utility function to convert SelectedPick to Pick format
const convertSelectedPickToPick = (selectedPick: SelectedPick): Pick => {
  return {
    id: selectedPick.sourceId,
    playerName: selectedPick.playerName,
    playerNumber: selectedPick.playerNumber,
    betType: selectedPick.betType,
    gameInfo: selectedPick.gameInfo,
    confidence: selectedPick.confidence || 50,
    expertCount: 3, // Default value - could be enhanced with real data
    additionalExperts: 2, // Default value - could be enhanced with real data
    handicapperNames: selectedPick.handicapperName
      ? [selectedPick.handicapperName]
      : [], // Convert single handicapper to array
  };
};

// PicksDropdown Component
function PicksDropdown({
  isVisible,
  onMouseEnter,
  onMouseLeave,
  onPickClick,
  isPinned,
  togglePin,
}: {
  isVisible: boolean;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
  onPickClick: (pick: SelectedPick) => void;
  isPinned: boolean;
  togglePin: () => void;
}) {
  const { getPicks, removePick } = usePicks();
  const picks = getPicks();

  if (!isVisible) {
    return null;
  }

  if (picks.length === 0) {
    return (
      <>
        {/* Invisible bridge to prevent dropdown from closing when moving cursor */}
        <div className="absolute top-full right-0 w-80 h-2 z-40" />
        <div
          className="absolute top-full right-0 mt-2 w-80 bg-[#233e6c] border border-gray-600 rounded-lg shadow-xl z-50 overflow-hidden"
          onMouseEnter={onMouseEnter}
          onMouseLeave={onMouseLeave}
        >
          <div className="p-4 text-center">
            <div className="text-gray-400 text-sm">No picks added yet</div>
            <div className="text-gray-500 text-xs mt-1">
              Add picks from the Picks or Handicappers view
            </div>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      {/* Invisible bridge to prevent dropdown from closing when moving cursor */}
      <div className="absolute top-full right-0 w-80 h-2 z-40" />
      <div
        className="absolute top-full right-0 mt-2 w-80 bg-[#233e6c] border border-gray-600 rounded-lg shadow-xl z-50 max-h-96 overflow-hidden"
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
      >
        <div className="p-3 border-b border-gray-600">
          <div className="flex items-center justify-between">
            <h3 className="text-white font-semibold text-sm">
              My Picks ({picks.length})
            </h3>
            <button
              onClick={(e) => {
                e.stopPropagation();
                togglePin();
              }}
              className="text-white hover:text-gray-300 p-1 rounded cursor-pointer transition-transform"
              title={isPinned ? "Unpin" : "Pin"}
            >
              <BsPin
                className={`w-4 h-4 transition-transform ${
                  isPinned ? "" : "-rotate-45"
                }`}
              />
            </button>
          </div>
        </div>
        <div className="max-h-80 overflow-y-auto picks-dropdown-scroll">
          {picks.map((pick) => (
            <div
              key={pick.id}
              className="p-3 border-b border-gray-700 last:border-b-0 hover:bg-[#1a2d54] transition-colors group cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                onPickClick(pick);
              }}
            >
              <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                  <div className="text-white font-medium text-sm truncate">
                    {pick.playerName} {pick.playerNumber}
                  </div>
                  <div className="text-gray-300 text-xs truncate">
                    {pick.betType}
                  </div>
                  <div className="text-gray-400 text-xs truncate">
                    {pick.gameInfo}
                  </div>
                  {pick.handicapperName && (
                    <div className="text-blue-400 text-xs truncate">
                      by {pick.handicapperName}
                    </div>
                  )}
                </div>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    removePick(pick.id);
                  }}
                  className="ml-2 p-1.5 text-gray-400 hover:text-red-400 hover:bg-red-500/20 rounded transition-colors flex-shrink-0 hover:cursor-pointer"
                  title="Remove pick"
                >
                  <HiTrash className="w-4 h-4" />
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </>
  );
}

function Header({ onBackToHome, onSearchChange, searchValue }: HeaderProps) {
  const { totalPicksCount } = usePicks();
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [isPinned, setIsPinned] = useState(false);
  const [selectedPick, setSelectedPick] = useState<Pick | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleMouseEnter = () => {
    setIsDropdownVisible(true);
  };

  const handleMouseLeave = () => {
    if (!isPinned) {
      setIsDropdownVisible(false);
    }
  };

  const handlePickClick = (selectedPick: SelectedPick) => {
    const pick = convertSelectedPickToPick(selectedPick);
    setSelectedPick(pick);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedPick(null);
  };

  const togglePin = () => {
    const newPinnedState = !isPinned;
    setIsPinned(newPinnedState);
    if (newPinnedState) {
      setIsDropdownVisible(true);
    }
  };

  return (
    <header className="w-full bg-[#061844] p-4 border-b border-gray-700">
      <div className="max-w-7xl mx-auto flex items-center justify-center gap-2 sm:gap-4">
        <button
          onClick={onBackToHome}
          className="flex items-center gap-2 px-4 py-2.5 bg-[#233e6c] hover:bg-[#1a2d54] text-white hover:text-gray-100 rounded-lg transition-all duration-200 shadow-sm font-medium hover:cursor-pointer"
        >
          <HiArrowLeft className="w-4 h-4" />
          <span className="text-sm hidden lg:inline">
            Back to Parlay Engine
          </span>
          <span className="text-sm lg:hidden">Back</span>
        </button>
        <HiBars3 className="w-8 h-8 text-white bg-[#233e6c] rounded p-1 cursor-pointer hover:bg-[#1a2d54] hover:text-gray-100 transition-colors flex-shrink-0 " />

        <img
          src="/project_parlay_logo.png"
          alt="Project Parlay Logo"
          className="h-10 sm:h-12 w-auto select-none flex-shrink-0 hover:cursor-pointer"
          onClick={onBackToHome}
          draggable="false"
        />

        <div className="flex-1 relative min-w-0 max-w-md sm:max-w-lg lg:max-w-xl">
          <HiMagnifyingGlass className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-white pointer-events-none" />
          <input
            type="text"
            placeholder="Search for handicappers, games, sports, teams..."
            className="w-full bg-[#233e6c] text-white placeholder-gray-400 rounded-lg py-4 pl-10 pr-20 focus:outline-none focus:ring-2 focus:ring-[#58C612] text-sm cursor-text"
            value={searchValue}
            onChange={(e) => {
              console.log(
                "Header: input onChange called with:",
                e.target.value
              );
              onSearchChange(e.target.value);
            }}
          />
          <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-2">
            <div className="h-6 w-px bg-gray-600"></div>
            <button className="flex items-center gap-1 text-white hover:text-gray-100 hover:cursor-pointer transition-colors">
              <span className="text-sm hidden sm:inline">Filter</span>
              <HiAdjustmentsHorizontal className="w-5 h-5" />
            </button>
          </div>
        </div>

        <div
          className="relative flex-shrink-0"
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          <HiListBullet
            onClick={onBackToHome}
            className="w-8 h-8 text-white bg-[#233e6c] rounded p-1 cursor-pointer hover:bg-[#1a2d54] hover:text-gray-100 transition-colors"
          />
          {totalPicksCount > 0 && (
            <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full min-w-[18px] h-[18px] flex items-center justify-center px-1 shadow-lg border-2 border-[#061844]">
              {totalPicksCount > 99 ? "99+" : totalPicksCount}
            </div>
          )}
          <PicksDropdown
            isVisible={isDropdownVisible || isPinned}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            onPickClick={handlePickClick}
            isPinned={isPinned}
            togglePin={togglePin}
          />
        </div>
      </div>

      {/* Pick Detail Modal */}
      <PickDetailModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        pick={selectedPick}
      />
    </header>
  );
}

export default Header;
