import React from "react";
import { getConfidenceColor } from "../../utils/colorUtils";

interface SubparlayPick {
  pID: string | number;
  odds: string;
  confidence?: number;
  name?: string;
  bayesian_conf?: number;
  bayesian_prob?: number;
  logistic_prob?: number;
  capital_limit?: number;
  event_id?: string;
  gameID?: number;
  league?: string;
  reusable?: boolean;
  stat_type?: string;
}

interface SubparlayColumn extends Array<SubparlayPick> {}

interface ExportOption {
  id: string;
  logoSrc?: string;
  exportType: "Promo" | "Best Line Value" | "Discount";
  exporterName: string;
  subparlays: SubparlayColumn[];
}

interface WhereToBetProps {
  subparlays: SubparlayColumn[];
}

function WhereToBet({ subparlays }: WhereToBetProps) {
  // Don't render if no parlays have been optimized yet
  if (!subparlays || subparlays.length === 0) {
    return null;
  }

  const mockExportOptions: ExportOption[] = [
    {
      id: "draftkings",
      logoSrc: "/draft-kings-logo.png",
      exportType: "Promo",
      exporterName: "Draft Kings",
      subparlays: subparlays,
    },
    {
      id: "prizepicks",
      logoSrc: "/prize-picks-logo.png",
      exportType: "Best Line Value",
      exporterName: "PrizePicks",
      subparlays: subparlays,
    },
    {
      id: "sleeper",
      logoSrc: "/sleeper-logo.png",
      exportType: "Discount",
      exporterName: "Sleeper",
      subparlays: subparlays,
    },
  ];

  const getExportTypeColor = (exportType: string): string => {
    switch (exportType) {
      case "Promo":
        return "#58C612";
      case "Best Line Value":
        return "#FFD700";
      case "Discount":
        return "#00D8FF";
      default:
        return "#58C612";
    }
  };

  return (
    <div className="min-h-[40vh] bg-[#051844] flex flex-col items-center justify-center pt-2 px-4 pb-4 sm:pt-3 sm:px-6 sm:pb-6 relative z-[50] overflow-visible">
      <div className="w-full max-w-7xl mx-auto">
        <div className="space-y-4 sm:space-y-6">
          {mockExportOptions.map((option) => (
            <div
              key={option.id}
              className="flex flex-col lg:grid lg:grid-cols-[minmax(120px,150px)_minmax(160px,200px)_1fr_minmax(140px,160px)] gap-4 lg:gap-6 items-center bg-[#051844] lg:bg-transparent rounded-lg lg:rounded-none p-4 lg:p-0"
            >
              {/* Logo Panel */}
              <div className="bg-[#233e6c] rounded-lg p-4 w-full lg:w-auto h-20 sm:h-24 flex items-center justify-center">
                {option.logoSrc ? (
                  <img
                    src={option.logoSrc}
                    alt={`${option.exporterName} logo`}
                    className="max-h-full max-w-full object-contain"
                  />
                ) : (
                  <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gray-600 rounded-lg flex items-center justify-center">
                    <span className="text-white text-xs text-center">
                      Logo
                      <br />
                      Placeholder
                    </span>
                  </div>
                )}
              </div>

              {/* Export Type Panel */}
              <div className="bg-[#233e6c] rounded-lg p-4 w-full lg:w-auto h-20 sm:h-24 flex items-center justify-center">
                <div
                  className="text-lg sm:text-2xl font-bold text-center leading-tight"
                  style={{ color: getExportTypeColor(option.exportType) }}
                >
                  {option.exportType}
                </div>
              </div>

              {/* Horizontal Parlay Scroll View */}
              <div className="bg-[#233e6c] rounded-lg p-4 w-full h-20 sm:h-24 order-last lg:order-none relative overflow-visible">
                {option.subparlays.length > 0 ? (
                  <div
                    className={`${
                      option.subparlays.length > 6
                        ? "overflow-x-auto parlay-scroll scrollbar-hide"
                        : "overflow-visible"
                    } relative z-[1] overflow-y-visible`}
                  >
                    <div
                      className={`flex gap-2 sm:gap-3 ${
                        option.subparlays.length <= 6
                          ? "justify-center"
                          : "justify-start"
                      } items-center relative overflow-visible`}
                      style={{
                        minWidth:
                          option.subparlays.length > 6
                            ? `${option.subparlays.length * 64}px`
                            : "auto",
                      }}
                    >
                      {option.subparlays.map((column, i) => (
                        <div
                          key={i}
                          data-parlay-column={i}
                          className="flex flex-col items-center flex-shrink-0 w-10 sm:w-12 relative overflow-visible z-[1]"
                          onMouseEnter={(e) => {
                            const column = e.currentTarget.closest(
                              "[data-parlay-column]"
                            );
                            if (column) {
                              (column as HTMLElement).style.zIndex = "999999";
                            }
                          }}
                          onMouseLeave={(e) => {
                            const column = e.currentTarget.closest(
                              "[data-parlay-column]"
                            );
                            if (column) {
                              (column as HTMLElement).style.zIndex = "1";
                            }
                          }}
                        >
                          <div className="text-white text-xs font-bold mb-1">
                            P{i + 1}
                          </div>
                          <div className="flex flex-col gap-1 w-full relative overflow-visible">
                            {column.slice(0, 3).map((pick) => (
                              <div
                                key={pick.pID}
                                className="h-2 sm:h-3 w-full rounded text-xs flex items-center justify-center text-white font-mono cursor-pointer transition-all duration-200 hover:scale-110 hover:shadow-lg relative group overflow-visible z-[1]"
                                style={{
                                  backgroundColor: getConfidenceColor(
                                    pick.confidence
                                  ),
                                }}
                                onMouseEnter={(e) => {
                                  const column = e.currentTarget.closest(
                                    "[data-parlay-column]"
                                  );
                                  if (column) {
                                    (column as HTMLElement).style.zIndex =
                                      "999999";
                                  }
                                }}
                                onMouseLeave={(e) => {
                                  const column = e.currentTarget.closest(
                                    "[data-parlay-column]"
                                  );
                                  if (column) {
                                    (column as HTMLElement).style.zIndex = "1";
                                  }
                                }}
                              >
                                {/* Comprehensive hover tooltip with highest z-index */}
                                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-4 py-3 bg-gray-900 text-white text-sm rounded-lg shadow-xl opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none min-w-[280px] z-[9999999]">
                                  <div className="space-y-2">
                                    {/* Player and Bet Info */}
                                    <div className="border-b border-gray-600 pb-2">
                                      <div className="font-bold text-green-400">
                                        {pick.name || `Pick #${pick.pID}`}
                                      </div>
                                      <div className="text-gray-300 text-xs">
                                        {pick.league} • {pick.stat_type}
                                      </div>
                                    </div>

                                    {/* Confidence and Odds */}
                                    <div className="grid grid-cols-2 gap-3">
                                      <div>
                                        <div className="text-white text-xs">
                                          Confidence
                                        </div>
                                        <div className="font-semibold text-white">
                                          {pick.confidence?.toFixed(0)}%
                                        </div>
                                      </div>
                                      <div>
                                        <div className="text-white text-xs">
                                          Odds
                                        </div>
                                        <div className="font-semibold text-white">
                                          {pick.odds}
                                        </div>
                                      </div>
                                    </div>

                                    {/* Probability Analysis */}
                                    <div className="border-t border-gray-600 pt-2">
                                      <div className="text-white text-xs mb-1">
                                        Probability Analysis
                                      </div>
                                      <div className="grid grid-cols-2 gap-2 text-xs">
                                        <div>
                                          <span className="text-white">
                                            Bayesian:
                                          </span>{" "}
                                          <span className="text-white">
                                            {pick.bayesian_prob
                                              ? (
                                                  pick.bayesian_prob * 100
                                                ).toFixed(1)
                                              : "N/A"}
                                            %
                                          </span>
                                        </div>
                                        <div>
                                          <span className="text-white">
                                            Logistic:
                                          </span>{" "}
                                          <span className="text-white">
                                            {pick.logistic_prob
                                              ? (
                                                  pick.logistic_prob * 100
                                                ).toFixed(1)
                                              : "N/A"}
                                            %
                                          </span>
                                        </div>
                                      </div>
                                    </div>

                                    {/* Additional Info */}
                                    <div className="border-t border-gray-600 pt-2 text-xs space-y-1">
                                      <div className="text-white">
                                        Event ID:{" "}
                                        <span className="text-white">
                                          {pick.event_id || "N/A"}
                                        </span>
                                      </div>
                                      <div className="text-white">
                                        Bayesian Conf:{" "}
                                        <span className="text-white">
                                          {pick.bayesian_conf?.toFixed(2) ||
                                            "N/A"}
                                        </span>
                                      </div>
                                      {pick.gameID && pick.gameID !== -1 && (
                                        <div className="text-white">
                                          Game ID:{" "}
                                          <span className="text-white">
                                            {pick.gameID}
                                          </span>
                                        </div>
                                      )}
                                      {pick.capital_limit !== undefined && (
                                        <div className="text-white">
                                          Capital Limit:{" "}
                                          <span className="text-white">
                                            {pick.capital_limit}
                                          </span>
                                        </div>
                                      )}
                                      {pick.reusable !== undefined && (
                                        <div className="text-white">
                                          Reusable:{" "}
                                          <span className="text-white">
                                            {pick.reusable ? "Yes" : "No"}
                                          </span>
                                        </div>
                                      )}
                                    </div>
                                  </div>

                                  {/* Tooltip arrow */}
                                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
                                </div>
                              </div>
                            ))}
                            {column.length > 3 && (
                              <div className="h-2 sm:h-3 w-full rounded bg-gray-600 text-xs flex items-center justify-center text-white cursor-pointer hover:bg-gray-500 transition-colors relative group">
                                +{column.length - 3}
                                {/* Additional picks tooltip */}
                                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg shadow-xl opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-[9999999]">
                                  {column.length - 3} more pick
                                  {column.length - 3 !== 1 ? "s" : ""} in this
                                  parlay
                                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="h-full flex items-center justify-center">
                    <span className="text-gray-400 text-sm">
                      No parlays available
                    </span>
                  </div>
                )}
              </div>

              {/* Export Button */}
              <div className="flex items-center justify-center w-full lg:w-auto">
                <button className="w-full lg:w-32 h-16 sm:h-20 bg-[#233e6c] hover:bg-[#1a2d54] text-white font-bold rounded-lg text-sm sm:text-base transition-all ease-linear duration-300 shadow-lg hover:shadow-xl hover:cursor-pointer flex items-center justify-center text-center leading-tight">
                  Place On
                  <br />
                  {option.exporterName}
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default WhereToBet;
